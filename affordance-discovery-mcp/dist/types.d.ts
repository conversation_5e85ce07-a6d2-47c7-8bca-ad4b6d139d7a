import { z } from 'zod';
export declare const PackageInfoSchema: z.ZodObject<{
    name: z.ZodString;
    version: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    main: z.ZodOptional<z.ZodString>;
    types: z.ZodOptional<z.ZodString>;
    exports: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
    dependencies: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodString>>;
    devDependencies: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodString>>;
    installed: z.ZodBoolean;
    path: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    name: string;
    version: string;
    installed: boolean;
    description?: string | undefined;
    main?: string | undefined;
    types?: string | undefined;
    path?: string | undefined;
    exports?: Record<string, any> | undefined;
    dependencies?: Record<string, string> | undefined;
    devDependencies?: Record<string, string> | undefined;
}, {
    name: string;
    version: string;
    installed: boolean;
    description?: string | undefined;
    main?: string | undefined;
    types?: string | undefined;
    path?: string | undefined;
    exports?: Record<string, any> | undefined;
    dependencies?: Record<string, string> | undefined;
    devDependencies?: Record<string, string> | undefined;
}>;
export declare const ModuleExportSchema: z.ZodObject<{
    name: z.ZodString;
    type: z.ZodEnum<["function", "class", "constant", "type", "interface", "namespace"]>;
    signature: z.ZodOptional<z.ZodString>;
    description: z.ZodOptional<z.ZodString>;
    parameters: z.ZodOptional<z.ZodArray<z.ZodObject<{
        name: z.ZodString;
        type: z.ZodString;
        optional: z.ZodDefault<z.ZodBoolean>;
        description: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        type: string;
        optional: boolean;
        description?: string | undefined;
    }, {
        name: string;
        type: string;
        description?: string | undefined;
        optional?: boolean | undefined;
    }>, "many">>;
    returnType: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    name: string;
    type: "function" | "type" | "class" | "constant" | "interface" | "namespace";
    description?: string | undefined;
    signature?: string | undefined;
    parameters?: {
        name: string;
        type: string;
        optional: boolean;
        description?: string | undefined;
    }[] | undefined;
    returnType?: string | undefined;
}, {
    name: string;
    type: "function" | "type" | "class" | "constant" | "interface" | "namespace";
    description?: string | undefined;
    signature?: string | undefined;
    parameters?: {
        name: string;
        type: string;
        description?: string | undefined;
        optional?: boolean | undefined;
    }[] | undefined;
    returnType?: string | undefined;
}>;
export declare const ModuleInfoSchema: z.ZodObject<{
    name: z.ZodString;
    path: z.ZodString;
    exports: z.ZodArray<z.ZodObject<{
        name: z.ZodString;
        type: z.ZodEnum<["function", "class", "constant", "type", "interface", "namespace"]>;
        signature: z.ZodOptional<z.ZodString>;
        description: z.ZodOptional<z.ZodString>;
        parameters: z.ZodOptional<z.ZodArray<z.ZodObject<{
            name: z.ZodString;
            type: z.ZodString;
            optional: z.ZodDefault<z.ZodBoolean>;
            description: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            name: string;
            type: string;
            optional: boolean;
            description?: string | undefined;
        }, {
            name: string;
            type: string;
            description?: string | undefined;
            optional?: boolean | undefined;
        }>, "many">>;
        returnType: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        type: "function" | "type" | "class" | "constant" | "interface" | "namespace";
        description?: string | undefined;
        signature?: string | undefined;
        parameters?: {
            name: string;
            type: string;
            optional: boolean;
            description?: string | undefined;
        }[] | undefined;
        returnType?: string | undefined;
    }, {
        name: string;
        type: "function" | "type" | "class" | "constant" | "interface" | "namespace";
        description?: string | undefined;
        signature?: string | undefined;
        parameters?: {
            name: string;
            type: string;
            description?: string | undefined;
            optional?: boolean | undefined;
        }[] | undefined;
        returnType?: string | undefined;
    }>, "many">;
    submodules: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    dependencies: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, "strip", z.ZodTypeAny, {
    name: string;
    path: string;
    exports: {
        name: string;
        type: "function" | "type" | "class" | "constant" | "interface" | "namespace";
        description?: string | undefined;
        signature?: string | undefined;
        parameters?: {
            name: string;
            type: string;
            optional: boolean;
            description?: string | undefined;
        }[] | undefined;
        returnType?: string | undefined;
    }[];
    dependencies?: string[] | undefined;
    submodules?: string[] | undefined;
}, {
    name: string;
    path: string;
    exports: {
        name: string;
        type: "function" | "type" | "class" | "constant" | "interface" | "namespace";
        description?: string | undefined;
        signature?: string | undefined;
        parameters?: {
            name: string;
            type: string;
            description?: string | undefined;
            optional?: boolean | undefined;
        }[] | undefined;
        returnType?: string | undefined;
    }[];
    dependencies?: string[] | undefined;
    submodules?: string[] | undefined;
}>;
export declare const DiscoveryResultSchema: z.ZodObject<{
    packages: z.ZodArray<z.ZodObject<{
        name: z.ZodString;
        version: z.ZodString;
        description: z.ZodOptional<z.ZodString>;
        main: z.ZodOptional<z.ZodString>;
        types: z.ZodOptional<z.ZodString>;
        exports: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
        dependencies: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodString>>;
        devDependencies: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodString>>;
        installed: z.ZodBoolean;
        path: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        version: string;
        installed: boolean;
        description?: string | undefined;
        main?: string | undefined;
        types?: string | undefined;
        path?: string | undefined;
        exports?: Record<string, any> | undefined;
        dependencies?: Record<string, string> | undefined;
        devDependencies?: Record<string, string> | undefined;
    }, {
        name: string;
        version: string;
        installed: boolean;
        description?: string | undefined;
        main?: string | undefined;
        types?: string | undefined;
        path?: string | undefined;
        exports?: Record<string, any> | undefined;
        dependencies?: Record<string, string> | undefined;
        devDependencies?: Record<string, string> | undefined;
    }>, "many">;
    totalFound: z.ZodNumber;
    searchTerm: z.ZodOptional<z.ZodString>;
    language: z.ZodEnum<["javascript", "python", "rust", "go", "java"]>;
    timestamp: z.ZodString;
}, "strip", z.ZodTypeAny, {
    packages: {
        name: string;
        version: string;
        installed: boolean;
        description?: string | undefined;
        main?: string | undefined;
        types?: string | undefined;
        path?: string | undefined;
        exports?: Record<string, any> | undefined;
        dependencies?: Record<string, string> | undefined;
        devDependencies?: Record<string, string> | undefined;
    }[];
    totalFound: number;
    language: "javascript" | "python" | "rust" | "go" | "java";
    timestamp: string;
    searchTerm?: string | undefined;
}, {
    packages: {
        name: string;
        version: string;
        installed: boolean;
        description?: string | undefined;
        main?: string | undefined;
        types?: string | undefined;
        path?: string | undefined;
        exports?: Record<string, any> | undefined;
        dependencies?: Record<string, string> | undefined;
        devDependencies?: Record<string, string> | undefined;
    }[];
    totalFound: number;
    language: "javascript" | "python" | "rust" | "go" | "java";
    timestamp: string;
    searchTerm?: string | undefined;
}>;
export declare const ValidationResultSchema: z.ZodObject<{
    valid: z.ZodBoolean;
    packageName: z.ZodString;
    modulePath: z.ZodOptional<z.ZodString>;
    reason: z.ZodOptional<z.ZodString>;
    suggestions: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, "strip", z.ZodTypeAny, {
    valid: boolean;
    packageName: string;
    modulePath?: string | undefined;
    reason?: string | undefined;
    suggestions?: string[] | undefined;
}, {
    valid: boolean;
    packageName: string;
    modulePath?: string | undefined;
    reason?: string | undefined;
    suggestions?: string[] | undefined;
}>;
export declare const DiscoverPackagesInputSchema: z.ZodObject<{
    language: z.ZodDefault<z.ZodEnum<["javascript", "python", "rust", "go", "java"]>>;
    searchTerm: z.ZodOptional<z.ZodString>;
    includeDevDependencies: z.ZodDefault<z.ZodBoolean>;
    maxResults: z.ZodDefault<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    language: "javascript" | "python" | "rust" | "go" | "java";
    includeDevDependencies: boolean;
    maxResults: number;
    searchTerm?: string | undefined;
}, {
    searchTerm?: string | undefined;
    language?: "javascript" | "python" | "rust" | "go" | "java" | undefined;
    includeDevDependencies?: boolean | undefined;
    maxResults?: number | undefined;
}>;
export declare const ValidateImportInputSchema: z.ZodObject<{
    importStatement: z.ZodString;
    language: z.ZodDefault<z.ZodEnum<["javascript", "python", "rust", "go", "java"]>>;
    projectPath: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    language: "javascript" | "python" | "rust" | "go" | "java";
    importStatement: string;
    projectPath?: string | undefined;
}, {
    importStatement: string;
    language?: "javascript" | "python" | "rust" | "go" | "java" | undefined;
    projectPath?: string | undefined;
}>;
export declare const IntrospectModuleInputSchema: z.ZodObject<{
    moduleName: z.ZodString;
    language: z.ZodDefault<z.ZodEnum<["javascript", "python", "rust", "go", "java"]>>;
    includePrivate: z.ZodDefault<z.ZodBoolean>;
    maxDepth: z.ZodDefault<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    language: "javascript" | "python" | "rust" | "go" | "java";
    moduleName: string;
    includePrivate: boolean;
    maxDepth: number;
}, {
    moduleName: string;
    language?: "javascript" | "python" | "rust" | "go" | "java" | undefined;
    includePrivate?: boolean | undefined;
    maxDepth?: number | undefined;
}>;
export declare const SearchAffordancesInputSchema: z.ZodObject<{
    query: z.ZodString;
    language: z.ZodDefault<z.ZodEnum<["javascript", "python", "rust", "go", "java"]>>;
    category: z.ZodDefault<z.ZodEnum<["ui", "data", "network", "testing", "build", "utility", "all"]>>;
    maxResults: z.ZodDefault<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    language: "javascript" | "python" | "rust" | "go" | "java";
    maxResults: number;
    query: string;
    category: "ui" | "data" | "network" | "testing" | "build" | "utility" | "all";
}, {
    query: string;
    language?: "javascript" | "python" | "rust" | "go" | "java" | undefined;
    maxResults?: number | undefined;
    category?: "ui" | "data" | "network" | "testing" | "build" | "utility" | "all" | undefined;
}>;
export type PackageInfo = z.infer<typeof PackageInfoSchema>;
export type ModuleExport = z.infer<typeof ModuleExportSchema>;
export type ModuleInfo = z.infer<typeof ModuleInfoSchema>;
export type DiscoveryResult = z.infer<typeof DiscoveryResultSchema>;
export type ValidationResult = z.infer<typeof ValidationResultSchema>;
export type DiscoverPackagesInput = z.infer<typeof DiscoverPackagesInputSchema>;
export type ValidateImportInput = z.infer<typeof ValidateImportInputSchema>;
export type IntrospectModuleInput = z.infer<typeof IntrospectModuleInputSchema>;
export type SearchAffordancesInput = z.infer<typeof SearchAffordancesInputSchema>;
export interface CacheEntry<T> {
    data: T;
    timestamp: number;
    ttl: number;
}
export interface CacheOptions {
    ttl: number;
    maxSize: number;
}
export interface DiscoveryEngine {
    discoverPackages(input: DiscoverPackagesInput): Promise<DiscoveryResult>;
    validateImport(input: ValidateImportInput): Promise<ValidationResult>;
    introspectModule(input: IntrospectModuleInput): Promise<ModuleInfo>;
    searchAffordances(input: SearchAffordancesInput): Promise<DiscoveryResult>;
}
//# sourceMappingURL=types.d.ts.map