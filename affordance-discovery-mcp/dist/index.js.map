{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,MAAM,EAAE,MAAM,2CAA2C,CAAC;AACnE,OAAO,EAAE,oBAAoB,EAAE,MAAM,2CAA2C,CAAC;AACjF,OAAO,EACL,qBAAqB,EACrB,sBAAsB,GAEvB,MAAM,oCAAoC,CAAC;AAC5C,OAAO,EAAE,yBAAyB,EAAE,MAAM,yBAAyB,CAAC;AACpE,OAAO,EAAE,YAAY,EAAE,MAAM,YAAY,CAAC;AAC1C,OAAO,EACL,2BAA2B,EAC3B,yBAAyB,EACzB,2BAA2B,EAC3B,4BAA4B,GAC7B,MAAM,YAAY,CAAC;AAEpB,MAAM,yBAAyB;IACrB,MAAM,CAAS;IACf,QAAQ,CAA4B;IACpC,KAAK,CAAe;IAE5B;QACE,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CACtB;YACE,IAAI,EAAE,0BAA0B;YAChC,OAAO,EAAE,OAAO;SACjB,EACD;YACE,YAAY,EAAE;gBACZ,KAAK,EAAE,EAAE;aACV;SACF,CACF,CAAC;QAEF,IAAI,CAAC,KAAK,GAAG,IAAI,YAAY,CAAC;YAC5B,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,YAAY;YAChC,OAAO,EAAE,IAAI;SACd,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,GAAG,IAAI,yBAAyB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE1D,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAEO,iBAAiB;QACvB,uBAAuB;QACvB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,KAAK,IAAI,EAAE;YAC/D,OAAO;gBACL,KAAK,EAAE;oBACL;wBACE,IAAI,EAAE,mBAAmB;wBACzB,WAAW,EAAE,wDAAwD;wBACrE,WAAW,EAAE;4BACX,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,QAAQ,EAAE;oCACR,IAAI,EAAE,QAAQ;oCACd,IAAI,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC;oCACpD,OAAO,EAAE,YAAY;oCACrB,WAAW,EAAE,6CAA6C;iCAC3D;gCACD,UAAU,EAAE;oCACV,IAAI,EAAE,QAAQ;oCACd,WAAW,EAAE,yCAAyC;iCACvD;gCACD,sBAAsB,EAAE;oCACtB,IAAI,EAAE,SAAS;oCACf,OAAO,EAAE,KAAK;oCACd,WAAW,EAAE,6CAA6C;iCAC3D;gCACD,UAAU,EAAE;oCACV,IAAI,EAAE,QAAQ;oCACd,OAAO,EAAE,CAAC;oCACV,OAAO,EAAE,GAAG;oCACZ,OAAO,EAAE,EAAE;oCACX,WAAW,EAAE,qCAAqC;iCACnD;6BACF;yBACF;qBACF;oBACD;wBACE,IAAI,EAAE,iBAAiB;wBACvB,WAAW,EAAE,6EAA6E;wBAC1F,WAAW,EAAE;4BACX,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,eAAe,EAAE;oCACf,IAAI,EAAE,QAAQ;oCACd,WAAW,EAAE,wEAAwE;iCACtF;gCACD,QAAQ,EAAE;oCACR,IAAI,EAAE,QAAQ;oCACd,IAAI,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC;oCACpD,OAAO,EAAE,YAAY;oCACrB,WAAW,EAAE,oCAAoC;iCAClD;gCACD,WAAW,EAAE;oCACX,IAAI,EAAE,QAAQ;oCACd,WAAW,EAAE,mCAAmC;iCACjD;6BACF;4BACD,QAAQ,EAAE,CAAC,iBAAiB,CAAC;yBAC9B;qBACF;oBACD;wBACE,IAAI,EAAE,mBAAmB;wBACzB,WAAW,EAAE,kEAAkE;wBAC/E,WAAW,EAAE;4BACX,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,UAAU,EAAE;oCACV,IAAI,EAAE,QAAQ;oCACd,WAAW,EAAE,kCAAkC;iCAChD;gCACD,QAAQ,EAAE;oCACR,IAAI,EAAE,QAAQ;oCACd,IAAI,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC;oCACpD,OAAO,EAAE,YAAY;oCACrB,WAAW,EAAE,oCAAoC;iCAClD;gCACD,cAAc,EAAE;oCACd,IAAI,EAAE,SAAS;oCACf,OAAO,EAAE,KAAK;oCACd,WAAW,EAAE,kCAAkC;iCAChD;gCACD,QAAQ,EAAE;oCACR,IAAI,EAAE,QAAQ;oCACd,OAAO,EAAE,CAAC;oCACV,OAAO,EAAE,CAAC;oCACV,OAAO,EAAE,CAAC;oCACV,WAAW,EAAE,wCAAwC;iCACtD;6BACF;4BACD,QAAQ,EAAE,CAAC,YAAY,CAAC;yBACzB;qBACF;oBACD;wBACE,IAAI,EAAE,oBAAoB;wBAC1B,WAAW,EAAE,8DAA8D;wBAC3E,WAAW,EAAE;4BACX,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,KAAK,EAAE;oCACL,IAAI,EAAE,QAAQ;oCACd,WAAW,EAAE,mDAAmD;iCACjE;gCACD,QAAQ,EAAE;oCACR,IAAI,EAAE,QAAQ;oCACd,IAAI,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC;oCACpD,OAAO,EAAE,YAAY;oCACrB,WAAW,EAAE,mCAAmC;iCACjD;gCACD,QAAQ,EAAE;oCACR,IAAI,EAAE,QAAQ;oCACd,IAAI,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC;oCACrE,OAAO,EAAE,KAAK;oCACd,WAAW,EAAE,+BAA+B;iCAC7C;gCACD,UAAU,EAAE;oCACV,IAAI,EAAE,QAAQ;oCACd,OAAO,EAAE,CAAC;oCACV,OAAO,EAAE,EAAE;oCACX,OAAO,EAAE,EAAE;oCACX,WAAW,EAAE,qCAAqC;iCACnD;6BACF;4BACD,QAAQ,EAAE,CAAC,OAAO,CAAC;yBACpB;qBACF;iBACe;aACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,oBAAoB;QACpB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;YACrE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;YAEjD,IAAI,CAAC;gBACH,QAAQ,IAAI,EAAE,CAAC;oBACb,KAAK,mBAAmB,CAAC,CAAC,CAAC;wBACzB,MAAM,KAAK,GAAG,2BAA2B,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBACtD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;wBAC3D,OAAO;4BACL,OAAO,EAAE;gCACP;oCACE,IAAI,EAAE,MAAM;oCACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;iCACtC;6BACF;yBACF,CAAC;oBACJ,CAAC;oBAED,KAAK,iBAAiB,CAAC,CAAC,CAAC;wBACvB,MAAM,KAAK,GAAG,yBAAyB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBACpD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;wBACzD,OAAO;4BACL,OAAO,EAAE;gCACP;oCACE,IAAI,EAAE,MAAM;oCACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;iCACtC;6BACF;yBACF,CAAC;oBACJ,CAAC;oBAED,KAAK,mBAAmB,CAAC,CAAC,CAAC;wBACzB,MAAM,KAAK,GAAG,2BAA2B,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBACtD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;wBAC3D,OAAO;4BACL,OAAO,EAAE;gCACP;oCACE,IAAI,EAAE,MAAM;oCACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;iCACtC;6BACF;yBACF,CAAC;oBACJ,CAAC;oBAED,KAAK,oBAAoB,CAAC,CAAC,CAAC;wBAC1B,MAAM,KAAK,GAAG,4BAA4B,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBACvD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;wBAC5D,OAAO;4BACL,OAAO,EAAE;gCACP;oCACE,IAAI,EAAE,MAAM;oCACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;iCACtC;6BACF;yBACF,CAAC;oBACJ,CAAC;oBAED;wBACE,MAAM,IAAI,KAAK,CAAC,iBAAiB,IAAI,EAAE,CAAC,CAAC;gBAC7C,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;gBAC9E,OAAO;oBACL,OAAO,EAAE;wBACP;4BACE,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,KAAK,EAAE,YAAY;gCACnB,IAAI,EAAE,IAAI;gCACV,SAAS,EAAE,IAAI;6BAChB,EAAE,IAAI,EAAE,CAAC,CAAC;yBACZ;qBACF;oBACD,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,kBAAkB;QACxB,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE;YAC9B,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC;QAEF,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;YAC9B,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,KAAK;QACT,MAAM,SAAS,GAAG,IAAI,oBAAoB,EAAE,CAAC;QAC7C,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACrC,OAAO,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;IACpE,CAAC;CACF;AAED,mBAAmB;AACnB,MAAM,MAAM,GAAG,IAAI,yBAAyB,EAAE,CAAC;AAC/C,MAAM,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;IAC7B,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;IAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC"}