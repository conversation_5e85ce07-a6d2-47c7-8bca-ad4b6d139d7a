import { CacheManager } from '../cache.js';
import { DiscoveryEngine, DiscoverPackagesInput, ValidateImportInput, IntrospectModuleInput, SearchAffordancesInput, DiscoveryResult, ValidationResult, ModuleInfo } from '../types.js';
export declare class JavaScriptDiscoveryEngine implements DiscoveryEngine {
    private cache;
    constructor(cache: CacheManager);
    discoverPackages(input: DiscoverPackagesInput): Promise<DiscoveryResult>;
    validateImport(input: ValidateImportInput): Promise<ValidationResult>;
    introspectModule(input: IntrospectModuleInput): Promise<ModuleInfo>;
    searchAffordances(input: SearchAffordancesInput): Promise<DiscoveryResult>;
    private findPackageJson;
    private getPackageInfo;
    private getGlobalPackages;
    private extractPackageNameFromImport;
    private checkPackageExists;
    private resolveModulePath;
    private getSimilarPackages;
    private analyzeModuleExports;
    private findSubmodules;
    private getModuleDependencies;
}
//# sourceMappingURL=javascript.d.ts.map