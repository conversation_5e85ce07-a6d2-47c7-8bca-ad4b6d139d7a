import { execSync } from 'child_process';
import { readFileSync, existsSync, readdirSync } from 'fs';
import { join, dirname } from 'path';
import { CacheManager } from '../cache.js';
export class JavaScriptDiscoveryEngine {
    cache;
    constructor(cache) {
        this.cache = cache;
    }
    async discoverPackages(input) {
        const cacheKey = CacheManager.generateKey('discover', input.language, input.searchTerm, input.includeDevDependencies, input.maxResults);
        // Check cache first
        const cached = this.cache.get(cacheKey);
        if (cached) {
            return cached;
        }
        const packages = [];
        try {
            // Find package.json in current directory or parent directories
            const packageJsonPath = this.findPackageJson(process.cwd());
            if (packageJsonPath) {
                const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
                const projectRoot = dirname(packageJsonPath);
                // Get installed packages from package.json
                const dependencies = packageJson.dependencies || {};
                const devDependencies = input.includeDevDependencies ? (packageJson.devDependencies || {}) : {};
                const allDeps = { ...dependencies, ...devDependencies };
                // Process each dependency
                for (const [name, version] of Object.entries(allDeps)) {
                    if (input.searchTerm && !name.toLowerCase().includes(input.searchTerm.toLowerCase())) {
                        continue;
                    }
                    const packageInfo = await this.getPackageInfo(name, version, projectRoot);
                    if (packageInfo) {
                        packages.push(packageInfo);
                    }
                    if (packages.length >= input.maxResults) {
                        break;
                    }
                }
            }
            // Also check globally installed packages
            if (packages.length < input.maxResults) {
                const globalPackages = await this.getGlobalPackages(input.searchTerm, input.maxResults - packages.length);
                packages.push(...globalPackages);
            }
        }
        catch (error) {
            console.error('Error discovering packages:', error);
        }
        const result = {
            packages: packages.slice(0, input.maxResults),
            totalFound: packages.length,
            searchTerm: input.searchTerm,
            language: 'javascript',
            timestamp: new Date().toISOString(),
        };
        // Cache the result
        this.cache.set(cacheKey, result);
        return result;
    }
    async validateImport(input) {
        const cacheKey = CacheManager.generateKey('validate', input.importStatement, input.language, input.projectPath);
        // Check cache first
        const cached = this.cache.get(cacheKey);
        if (cached) {
            return cached;
        }
        try {
            // Parse the import statement to extract package name
            const packageName = this.extractPackageNameFromImport(input.importStatement);
            if (!packageName) {
                const result = {
                    valid: false,
                    packageName: 'unknown',
                    reason: 'Could not parse import statement',
                    suggestions: [],
                };
                this.cache.set(cacheKey, result);
                return result;
            }
            // Check if package exists
            const projectRoot = input.projectPath || process.cwd();
            const packageExists = await this.checkPackageExists(packageName, projectRoot);
            if (packageExists) {
                const result = {
                    valid: true,
                    packageName,
                    modulePath: this.resolveModulePath(packageName, projectRoot) || undefined,
                };
                this.cache.set(cacheKey, result);
                return result;
            }
            else {
                const suggestions = await this.getSimilarPackages(packageName);
                const result = {
                    valid: false,
                    packageName,
                    reason: `Package '${packageName}' is not installed or available`,
                    suggestions,
                };
                this.cache.set(cacheKey, result);
                return result;
            }
        }
        catch (error) {
            const result = {
                valid: false,
                packageName: 'unknown',
                reason: `Error validating import: ${error instanceof Error ? error.message : 'Unknown error'}`,
                suggestions: [],
            };
            this.cache.set(cacheKey, result);
            return result;
        }
    }
    async introspectModule(input) {
        const cacheKey = CacheManager.generateKey('introspect', input.moduleName, input.language, input.includePrivate, input.maxDepth);
        // Check cache first
        const cached = this.cache.get(cacheKey);
        if (cached) {
            return cached;
        }
        try {
            const modulePath = this.resolveModulePath(input.moduleName, process.cwd());
            const exports = await this.analyzeModuleExports(input.moduleName, modulePath, input.includePrivate);
            const result = {
                name: input.moduleName,
                path: modulePath || '',
                exports,
                submodules: await this.findSubmodules(input.moduleName, modulePath),
                dependencies: await this.getModuleDependencies(input.moduleName),
            };
            this.cache.set(cacheKey, result);
            return result;
        }
        catch (error) {
            const result = {
                name: input.moduleName,
                path: '',
                exports: [],
                submodules: [],
                dependencies: [],
            };
            this.cache.set(cacheKey, result);
            return result;
        }
    }
    async searchAffordances(input) {
        // For now, delegate to discoverPackages with search term
        return this.discoverPackages({
            language: input.language,
            searchTerm: input.query,
            includeDevDependencies: true,
            maxResults: input.maxResults,
        });
    }
    // Helper methods
    findPackageJson(startDir) {
        let currentDir = startDir;
        while (currentDir !== '/') {
            const packageJsonPath = join(currentDir, 'package.json');
            if (existsSync(packageJsonPath)) {
                return packageJsonPath;
            }
            currentDir = dirname(currentDir);
        }
        return null;
    }
    async getPackageInfo(name, version, projectRoot) {
        try {
            const packagePath = join(projectRoot, 'node_modules', name);
            const packageJsonPath = join(packagePath, 'package.json');
            if (!existsSync(packageJsonPath)) {
                return {
                    name,
                    version,
                    installed: false,
                };
            }
            const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
            return {
                name,
                version: packageJson.version || version,
                description: packageJson.description,
                main: packageJson.main,
                types: packageJson.types || packageJson.typings,
                exports: packageJson.exports,
                dependencies: packageJson.dependencies,
                devDependencies: packageJson.devDependencies,
                installed: true,
                path: packagePath,
            };
        }
        catch (error) {
            return {
                name,
                version,
                installed: false,
            };
        }
    }
    async getGlobalPackages(searchTerm, maxResults = 10) {
        try {
            const output = execSync('npm list -g --depth=0 --json', { encoding: 'utf-8' });
            const globalPackages = JSON.parse(output);
            const packages = [];
            if (globalPackages.dependencies) {
                for (const [name, info] of Object.entries(globalPackages.dependencies)) {
                    if (searchTerm && !name.toLowerCase().includes(searchTerm.toLowerCase())) {
                        continue;
                    }
                    const packageInfo = info;
                    packages.push({
                        name,
                        version: packageInfo.version || 'unknown',
                        installed: true,
                        path: packageInfo.path,
                    });
                    if (packages.length >= maxResults) {
                        break;
                    }
                }
            }
            return packages;
        }
        catch (error) {
            return [];
        }
    }
    extractPackageNameFromImport(importStatement) {
        // Handle various import patterns
        const patterns = [
            /import\s+.*\s+from\s+['"]([^'"]+)['"]/, // import ... from 'package'
            /import\s*\(\s*['"]([^'"]+)['"]\s*\)/, // import('package')
            /require\s*\(\s*['"]([^'"]+)['"]\s*\)/, // require('package')
        ];
        for (const pattern of patterns) {
            const match = importStatement.match(pattern);
            if (match && match[1]) {
                const fullPath = match[1];
                // Extract package name (handle scoped packages)
                if (fullPath.startsWith('@')) {
                    const parts = fullPath.split('/');
                    return parts.length >= 2 ? `${parts[0]}/${parts[1]}` : fullPath;
                }
                else {
                    return fullPath.split('/')[0] || null;
                }
            }
        }
        return null;
    }
    async checkPackageExists(packageName, projectRoot) {
        try {
            // Check in node_modules
            const packagePath = join(projectRoot, 'node_modules', packageName);
            if (existsSync(packagePath)) {
                return true;
            }
            // Try to resolve using Node.js resolution
            try {
                require.resolve(packageName, { paths: [projectRoot] });
                return true;
            }
            catch {
                return false;
            }
        }
        catch {
            return false;
        }
    }
    resolveModulePath(moduleName, projectRoot) {
        try {
            return require.resolve(moduleName, { paths: [projectRoot] });
        }
        catch {
            return null;
        }
    }
    async getSimilarPackages(packageName) {
        // Simple similarity check - in a real implementation, you might use fuzzy matching
        try {
            const packageJsonPath = this.findPackageJson(process.cwd());
            if (!packageJsonPath)
                return [];
            const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
            const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };
            return Object.keys(allDeps)
                .filter(name => name.includes(packageName) || packageName.includes(name))
                .slice(0, 5);
        }
        catch {
            return [];
        }
    }
    async analyzeModuleExports(moduleName, modulePath, includePrivate) {
        // This is a simplified implementation - in practice, you'd want to use AST parsing
        const exports = [];
        try {
            if (modulePath) {
                // Try to dynamically import and analyze the module
                const module = await import(modulePath);
                for (const [key, value] of Object.entries(module)) {
                    if (!includePrivate && key.startsWith('_')) {
                        continue;
                    }
                    const type = typeof value === 'function' ? 'function' :
                        typeof value === 'object' && value?.constructor?.name === 'Object' ? 'namespace' :
                            'constant';
                    exports.push({
                        name: key,
                        type,
                        signature: type === 'function' ? `${key}()` : undefined,
                    });
                }
            }
        }
        catch (error) {
            // Fallback: try to read package.json exports
            try {
                const packagePath = join(dirname(modulePath || ''), 'package.json');
                if (existsSync(packagePath)) {
                    const packageJson = JSON.parse(readFileSync(packagePath, 'utf-8'));
                    if (packageJson.exports) {
                        for (const exportPath of Object.keys(packageJson.exports)) {
                            exports.push({
                                name: exportPath === '.' ? 'default' : exportPath,
                                type: 'namespace',
                            });
                        }
                    }
                }
            }
            catch {
                // Ignore errors
            }
        }
        return exports;
    }
    async findSubmodules(moduleName, modulePath) {
        if (!modulePath)
            return [];
        try {
            const moduleDir = dirname(modulePath);
            const files = readdirSync(moduleDir);
            return files
                .filter(file => file.endsWith('.js') || file.endsWith('.ts'))
                .filter(file => file !== 'index.js' && file !== 'index.ts')
                .map(file => file.replace(/\.(js|ts)$/, ''))
                .slice(0, 10); // Limit results
        }
        catch {
            return [];
        }
    }
    async getModuleDependencies(moduleName) {
        try {
            const packagePath = join(process.cwd(), 'node_modules', moduleName, 'package.json');
            if (existsSync(packagePath)) {
                const packageJson = JSON.parse(readFileSync(packagePath, 'utf-8'));
                return Object.keys(packageJson.dependencies || {});
            }
        }
        catch {
            // Ignore errors
        }
        return [];
    }
}
//# sourceMappingURL=javascript.js.map