import { CacheOptions } from './types.js';
export declare class CacheManager {
    private cache;
    private options;
    constructor(options: CacheOptions);
    /**
     * Get a cached value if it exists and hasn't expired
     */
    get<T>(key: string): T | null;
    /**
     * Set a value in the cache
     */
    set<T>(key: string, value: T, customTtl?: number): void;
    /**
     * Check if a key exists in cache and hasn't expired
     */
    has(key: string): boolean;
    /**
     * Delete a specific key from cache
     */
    delete(key: string): boolean;
    /**
     * Clear all cache entries
     */
    clear(): void;
    /**
     * Get cache statistics
     */
    getStats(): {
        size: number;
        maxSize: number;
        hitRate: number;
        memoryUsage: number;
    };
    /**
     * Remove expired entries from cache
     */
    private cleanup;
    /**
     * Estimate memory usage of the cache (rough approximation)
     */
    private estimateMemoryUsage;
    /**
     * Generate a cache key from multiple parameters
     */
    static generateKey(...parts: (string | number | boolean | undefined)[]): string;
}
//# sourceMappingURL=cache.d.ts.map