export class CacheManager {
    cache = new Map();
    options;
    constructor(options) {
        this.options = options;
        // Clean up expired entries periodically
        setInterval(() => {
            this.cleanup();
        }, this.options.ttl / 2);
    }
    /**
     * Get a cached value if it exists and hasn't expired
     */
    get(key) {
        const entry = this.cache.get(key);
        if (!entry) {
            return null;
        }
        // Check if expired
        if (Date.now() - entry.timestamp > entry.ttl) {
            this.cache.delete(key);
            return null;
        }
        return entry.data;
    }
    /**
     * Set a value in the cache
     */
    set(key, value, customTtl) {
        // If cache is full, remove oldest entry
        if (this.cache.size >= this.options.maxSize) {
            const oldestKey = this.cache.keys().next().value;
            if (oldestKey) {
                this.cache.delete(oldestKey);
            }
        }
        const entry = {
            data: value,
            timestamp: Date.now(),
            ttl: customTtl ?? this.options.ttl,
        };
        this.cache.set(key, entry);
    }
    /**
     * Check if a key exists in cache and hasn't expired
     */
    has(key) {
        return this.get(key) !== null;
    }
    /**
     * Delete a specific key from cache
     */
    delete(key) {
        return this.cache.delete(key);
    }
    /**
     * Clear all cache entries
     */
    clear() {
        this.cache.clear();
    }
    /**
     * Get cache statistics
     */
    getStats() {
        return {
            size: this.cache.size,
            maxSize: this.options.maxSize,
            hitRate: 0, // TODO: Implement hit rate tracking
            memoryUsage: this.estimateMemoryUsage(),
        };
    }
    /**
     * Remove expired entries from cache
     */
    cleanup() {
        const now = Date.now();
        const keysToDelete = [];
        for (const [key, entry] of this.cache.entries()) {
            if (now - entry.timestamp > entry.ttl) {
                keysToDelete.push(key);
            }
        }
        for (const key of keysToDelete) {
            this.cache.delete(key);
        }
    }
    /**
     * Estimate memory usage of the cache (rough approximation)
     */
    estimateMemoryUsage() {
        let totalSize = 0;
        for (const [key, entry] of this.cache.entries()) {
            // Rough estimation: key size + JSON string size of data
            totalSize += key.length * 2; // UTF-16 characters
            totalSize += JSON.stringify(entry.data).length * 2;
            totalSize += 24; // Approximate overhead for timestamp, ttl, etc.
        }
        return totalSize;
    }
    /**
     * Generate a cache key from multiple parameters
     */
    static generateKey(...parts) {
        return parts
            .filter(part => part !== undefined)
            .map(part => String(part))
            .join(':');
    }
}
//# sourceMappingURL=cache.js.map